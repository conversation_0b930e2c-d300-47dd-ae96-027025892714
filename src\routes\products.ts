import { User<PERSON><PERSON> } from "@prisma/client";
import { Router } from "express";
import { ProductController } from "../controllers/productController";
import { authenticate, authorize } from "../middleware/auth";
import { validate } from "../middleware/validation";
import {
  createProductSchema,
  productParamsSchema,
  productQuerySchema,
  updateProductSchema,
  updateStockSchema,
} from "../schemas/product";

const router = Router();

// All product routes require authentication
router.use(authenticate);

// Get all products (All authenticated users)
router.get("/", validate(productQuerySchema), ProductController.getProducts);

// Create product (ADMIN and MANAGER only)
router.post(
  "/",
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  validate(createProductSchema),
  ProductController.createProduct
);

// Get product by ID (All authenticated users)
router.get(
  "/:id",
  validate(productParamsSchema),
  ProductController.getProductById
);

// Update product (ADMIN and MANAGER only)
router.put(
  "/:id",
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  validate(productParamsSchema),
  validate(updateProductSchema),
  ProductController.updateProduct
);

// Delete product (ADMIN only)
router.delete(
  "/:id",
  authorize(UserRole.ADMIN),
  validate(productParamsSchema),
  ProductController.deleteProduct
);

// Update product stock (ADMIN and MANAGER only)
router.put(
  "/:id/stock",
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  validate(productParamsSchema),
  validate(updateStockSchema),
  ProductController.updateStock
);

export default router;
