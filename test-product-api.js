const https = require("http");

// Test data
const loginData = {
  email: "<EMAIL>",
  password: "admin123",
};

// Function to make HTTP requests
function makeRequest(options, data) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let body = "";
      res.on("data", (chunk) => {
        body += chunk;
      });
      res.on("end", () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on("error", (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testProductAPI() {
  console.log("🧪 Testing Product Management API...\n");

  try {
    // Step 1: Login to get JWT token
    console.log("1. Logging in as admin...");
    const loginOptions = {
      hostname: "localhost",
      port: 3000,
      path: "/api/auth/login",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    };

    const loginResponse = await makeRequest(loginOptions, loginData);
    console.log("Login response:", loginResponse);

    if (loginResponse.status !== 200 || !loginResponse.data.success) {
      throw new Error("Login failed");
    }

    const token = loginResponse.data.data.token;
    console.log("✅ Login successful, token received\n");

    // Step 2: Get categories to find confectionery category
    console.log("2. Getting categories...");
    const categoriesOptions = {
      hostname: "localhost",
      port: 3000,
      path: "/api/categories?page=1&limit=10",
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    };

    const categoriesResponse = await makeRequest(categoriesOptions);
    console.log("Categories response:", categoriesResponse);

    if (categoriesResponse.status !== 200) {
      throw new Error("Failed to get categories");
    }

    const confectioneryCategory = categoriesResponse.data.data.find((cat) =>
      cat.name.toLowerCase().includes("confectionery")
    );

    if (!confectioneryCategory) {
      throw new Error("Confectionery category not found");
    }

    console.log(
      "✅ Found confectionery category:",
      confectioneryCategory.name,
      confectioneryCategory.id,
      "\n"
    );

    // Step 3: Create "Blum Ada" product
    console.log('3. Creating "Blum Ada" product...');
    const blumAdaProduct = {
      name: "Blum Ada",
      description:
        "Premium confectionery item with rich flavor and elegant presentation",
      price: 12.95,
      cost: 5.5,
      sku: "CONF_BLUM_001",
      barcode: "1234567890123",
      type: "CONFECTIONERY",
      categoryId: confectioneryCategory.id,
      stockQuantity: 25,
      minStockLevel: 5,
      trackInventory: true,
      calories: 320,
      allergens: "Contains nuts, dairy",
    };

    const createProductOptions = {
      hostname: "localhost",
      port: 3000,
      path: "/api/products",
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    };

    const createResponse = await makeRequest(
      createProductOptions,
      blumAdaProduct
    );
    console.log("Create product response:", createResponse);

    if (createResponse.status !== 201) {
      throw new Error("Failed to create product");
    }

    const createdProduct = createResponse.data.data;
    console.log('✅ "Blum Ada" product created successfully!');
    console.log("Product ID:", createdProduct.id);
    console.log("Product Name:", createdProduct.name);
    console.log("Price:", createdProduct.price);
    console.log("Stock:", createdProduct.stockQuantity, "\n");

    // Step 4: Test stock update
    console.log("4. Testing stock update...");
    const stockUpdateData = {
      quantity: 10,
      type: "ADD",
      reason: "New shipment received",
    };

    const stockUpdateOptions = {
      hostname: "localhost",
      port: 3000,
      path: `/api/products/${createdProduct.id}/stock`,
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    };

    const stockResponse = await makeRequest(
      stockUpdateOptions,
      stockUpdateData
    );
    console.log("Stock update response:", stockResponse);

    if (stockResponse.status !== 200) {
      throw new Error("Failed to update stock");
    }

    console.log("✅ Stock updated successfully!");
    console.log(
      "New stock quantity:",
      stockResponse.data.data.stockQuantity,
      "\n"
    );

    // Step 5: Get all products to verify
    console.log("5. Getting all products...");
    const getProductsOptions = {
      hostname: "localhost",
      port: 3000,
      path: "/api/products?page=1&limit=10",
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    };

    const productsResponse = await makeRequest(getProductsOptions);
    console.log("Products response status:", productsResponse.status);

    if (productsResponse.status === 200) {
      const products = productsResponse.data.data;
      console.log("✅ Found", products.length, "products");
      const blumAda = products.find((p) => p.name === "Blum Ada");
      if (blumAda) {
        console.log('✅ "Blum Ada" found in product list!');
        console.log("Final stock quantity:", blumAda.stockQuantity);
      }
    }

    console.log("\n🎉 All tests completed successfully!");
  } catch (error) {
    console.error("❌ Test failed:", error.message);
    console.error("Error details:", error);
  }
}

// Run the test
testProductAPI();
