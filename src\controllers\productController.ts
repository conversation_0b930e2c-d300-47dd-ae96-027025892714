import { Request, Response } from 'express';
import { ProductService } from '../services/productService';
import { asyncHandler } from '../middleware/errorHandler';
import { 
  CreateProductInput, 
  UpdateProductInput, 
  ProductParams, 
  ProductQuery,
  UpdateStockInput 
} from '../schemas/product';

export class ProductController {
  static createProduct = asyncHandler(async (req: Request, res: Response) => {
    const data: CreateProductInput = req.body;
    const product = await ProductService.createProduct(data);
    
    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: product
    });
  });

  static getProducts = asyncHandler(async (req: Request, res: Response) => {
    const query: ProductQuery = req.query as any;
    const result = await ProductService.getProducts(query);
    
    res.status(200).json({
      success: true,
      message: 'Products retrieved successfully',
      data: result.products,
      pagination: result.pagination
    });
  });

  static getProductById = asyncHandler(async (req: Request, res: Response) => {
    const { id }: ProductParams = req.params as any;
    const product = await ProductService.getProductById(id);
    
    res.status(200).json({
      success: true,
      message: 'Product retrieved successfully',
      data: product
    });
  });

  static updateProduct = asyncHandler(async (req: Request, res: Response) => {
    const { id }: ProductParams = req.params as any;
    const data: UpdateProductInput = req.body;
    const product = await ProductService.updateProduct(id, data);
    
    res.status(200).json({
      success: true,
      message: 'Product updated successfully',
      data: product
    });
  });

  static deleteProduct = asyncHandler(async (req: Request, res: Response) => {
    const { id }: ProductParams = req.params as any;
    const result = await ProductService.deleteProduct(id);
    
    res.status(200).json({
      success: true,
      message: result.message,
      data: null
    });
  });

  static updateStock = asyncHandler(async (req: Request, res: Response) => {
    const { id }: ProductParams = req.params as any;
    const data: UpdateStockInput = req.body;
    const product = await ProductService.updateStock(id, data);
    
    res.status(200).json({
      success: true,
      message: 'Product stock updated successfully',
      data: product
    });
  });
}
