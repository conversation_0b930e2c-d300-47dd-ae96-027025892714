import { prisma } from "../config/database";
import { logger } from "../config/logger";
import { CustomError } from "../middleware/errorHandler";
import {
  CreateProductInput,
  ProductQuery,
  UpdateProductInput,
} from "../schemas/product";

export class ProductService {
  static async createProduct(data: CreateProductInput) {
    try {
      // Check if category exists
      const category = await prisma.category.findUnique({
        where: { id: data.categoryId },
      });

      if (!category) {
        throw new CustomError("Category not found", 404);
      }

      if (!category.isActive) {
        throw new CustomError(
          "Cannot create product in inactive category",
          400
        );
      }

      // Check for SKU uniqueness if provided
      if (data.sku) {
        const existingProduct = await prisma.product.findUnique({
          where: { sku: data.sku },
        });

        if (existingProduct) {
          throw new CustomError("Product with this SKU already exists", 409);
        }
      }

      // Check for barcode uniqueness if provided
      if (data.barcode) {
        const existingProduct = await prisma.product.findUnique({
          where: { barcode: data.barcode },
        });

        if (existingProduct) {
          throw new CustomError(
            "Product with this barcode already exists",
            409
          );
        }
      }

      const product = await prisma.product.create({
        data,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              isActive: true,
            },
          },
        },
      });

      logger.info(`Product created: ${product.name} (ID: ${product.id})`);
      return product;
    } catch (error) {
      logger.error("Product creation failed:", error);
      throw error;
    }
  }

  static async getProducts(query: ProductQuery) {
    try {
      const { page, limit, search, categoryId, type, isActive, lowStock } =
        query;
      const skip = (page - 1) * limit;

      const where: any = {};

      // Search functionality
      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
          { sku: { contains: search, mode: "insensitive" } },
          { barcode: { contains: search, mode: "insensitive" } },
        ];
      }

      // Filter by category
      if (categoryId) {
        where.categoryId = categoryId;
      }

      // Filter by product type
      if (type) {
        where.type = type;
      }

      // Filter by active status
      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      // Filter for low stock products - we'll handle this with a raw query approach
      if (lowStock) {
        where.trackInventory = true;
        // We'll filter this in the application layer since Prisma doesn't support field references in where clauses
      }

      const [products, total] = await Promise.all([
        prisma.product.findMany({
          where,
          include: {
            category: {
              select: {
                id: true,
                name: true,
                isActive: true,
              },
            },
          },
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
        }),
        prisma.product.count({ where }),
      ]);

      return {
        products,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error("Failed to fetch products:", error);
      throw error;
    }
  }

  static async getProductById(id: string) {
    try {
      const product = await prisma.product.findUnique({
        where: { id },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              isActive: true,
            },
          },
        },
      });

      if (!product) {
        throw new CustomError("Product not found", 404);
      }

      return product;
    } catch (error) {
      logger.error("Failed to fetch product:", error);
      throw error;
    }
  }

  static async updateProduct(id: string, data: UpdateProductInput) {
    try {
      const existingProduct = await prisma.product.findUnique({
        where: { id },
      });

      if (!existingProduct) {
        throw new CustomError("Product not found", 404);
      }

      // Check category exists if being updated
      if (data.categoryId) {
        const category = await prisma.category.findUnique({
          where: { id: data.categoryId },
        });

        if (!category) {
          throw new CustomError("Category not found", 404);
        }

        if (!category.isActive) {
          throw new CustomError(
            "Cannot assign product to inactive category",
            400
          );
        }
      }

      // Check for SKU conflicts if being updated
      if (data.sku) {
        const conflictProduct = await prisma.product.findFirst({
          where: {
            AND: [{ id: { not: id } }, { sku: data.sku }],
          },
        });

        if (conflictProduct) {
          throw new CustomError("Product with this SKU already exists", 409);
        }
      }

      // Check for barcode conflicts if being updated
      if (data.barcode) {
        const conflictProduct = await prisma.product.findFirst({
          where: {
            AND: [{ id: { not: id } }, { barcode: data.barcode }],
          },
        });

        if (conflictProduct) {
          throw new CustomError(
            "Product with this barcode already exists",
            409
          );
        }
      }

      const product = await prisma.product.update({
        where: { id },
        data,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              isActive: true,
            },
          },
        },
      });

      logger.info(`Product updated: ${product.name} (ID: ${product.id})`);
      return product;
    } catch (error) {
      logger.error("Product update failed:", error);
      throw error;
    }
  }
}
