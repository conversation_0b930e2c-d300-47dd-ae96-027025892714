import { prisma } from "../config/database";
import { logger } from "../config/logger";
import { CustomError } from "../middleware/errorHandler";
import {
  CreateProductInput,
  ProductQuery,
  UpdateProductInput,
  UpdateStockInput,
} from "../schemas/product";

export class ProductService {
  static async createProduct(data: CreateProductInput) {
    try {
      // Check if category exists
      const category = await prisma.category.findUnique({
        where: { id: data.categoryId },
      });

      if (!category) {
        throw new CustomError("Category not found", 404);
      }

      if (!category.isActive) {
        throw new CustomError(
          "Cannot create product in inactive category",
          400
        );
      }

      // Check for SKU uniqueness if provided
      if (data.sku) {
        const existingProduct = await prisma.product.findUnique({
          where: { sku: data.sku },
        });

        if (existingProduct) {
          throw new CustomError("Product with this SKU already exists", 409);
        }
      }

      // Check for barcode uniqueness if provided
      if (data.barcode) {
        const existingProduct = await prisma.product.findUnique({
          where: { barcode: data.barcode },
        });

        if (existingProduct) {
          throw new CustomError(
            "Product with this barcode already exists",
            409
          );
        }
      }

      const product = await prisma.product.create({
        data,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              isActive: true,
            },
          },
        },
      });

      logger.info(`Product created: ${product.name} (ID: ${product.id})`);
      return product;
    } catch (error) {
      logger.error("Product creation failed:", error);
      throw error;
    }
  }

  static async getProducts(query: ProductQuery) {
    try {
      const { page, limit, search, categoryId, type, isActive, lowStock } =
        query;
      const skip = (page - 1) * limit;

      const where: any = {};

      // Search functionality
      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
          { sku: { contains: search, mode: "insensitive" } },
          { barcode: { contains: search, mode: "insensitive" } },
        ];
      }

      // Filter by category
      if (categoryId) {
        where.categoryId = categoryId;
      }

      // Filter by product type
      if (type) {
        where.type = type;
      }

      // Filter by active status
      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      // For low stock filtering, we need to handle it differently
      let products, total;

      if (lowStock) {
        where.trackInventory = true;

        // Get all products that track inventory first
        const allProducts = await prisma.product.findMany({
          where,
          include: {
            category: {
              select: {
                id: true,
                name: true,
                isActive: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        });

        // Filter for low stock in application layer
        const lowStockProducts = allProducts.filter(
          (product) => product.stockQuantity <= product.minStockLevel
        );

        total = lowStockProducts.length;
        products = lowStockProducts.slice(skip, skip + limit);
      } else {
        [products, total] = await Promise.all([
          prisma.product.findMany({
            where,
            include: {
              category: {
                select: {
                  id: true,
                  name: true,
                  isActive: true,
                },
              },
            },
            skip,
            take: limit,
            orderBy: { createdAt: "desc" },
          }),
          prisma.product.count({ where }),
        ]);
      }

      return {
        products,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error("Failed to fetch products:", error);
      throw error;
    }
  }

  static async getProductById(id: string) {
    try {
      const product = await prisma.product.findUnique({
        where: { id },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              isActive: true,
            },
          },
        },
      });

      if (!product) {
        throw new CustomError("Product not found", 404);
      }

      return product;
    } catch (error) {
      logger.error("Failed to fetch product:", error);
      throw error;
    }
  }

  static async updateProduct(id: string, data: UpdateProductInput) {
    try {
      const existingProduct = await prisma.product.findUnique({
        where: { id },
      });

      if (!existingProduct) {
        throw new CustomError("Product not found", 404);
      }

      // Check category exists if being updated
      if (data.categoryId) {
        const category = await prisma.category.findUnique({
          where: { id: data.categoryId },
        });

        if (!category) {
          throw new CustomError("Category not found", 404);
        }

        if (!category.isActive) {
          throw new CustomError(
            "Cannot assign product to inactive category",
            400
          );
        }
      }

      // Check for SKU conflicts if being updated
      if (data.sku) {
        const conflictProduct = await prisma.product.findFirst({
          where: {
            AND: [{ id: { not: id } }, { sku: data.sku }],
          },
        });

        if (conflictProduct) {
          throw new CustomError("Product with this SKU already exists", 409);
        }
      }

      // Check for barcode conflicts if being updated
      if (data.barcode) {
        const conflictProduct = await prisma.product.findFirst({
          where: {
            AND: [{ id: { not: id } }, { barcode: data.barcode }],
          },
        });

        if (conflictProduct) {
          throw new CustomError(
            "Product with this barcode already exists",
            409
          );
        }
      }

      const product = await prisma.product.update({
        where: { id },
        data,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              isActive: true,
            },
          },
        },
      });

      logger.info(`Product updated: ${product.name} (ID: ${product.id})`);
      return product;
    } catch (error) {
      logger.error("Product update failed:", error);
      throw error;
    }
  }

  static async deleteProduct(id: string) {
    try {
      const existingProduct = await prisma.product.findUnique({
        where: { id },
        include: {
          orderItems: true,
        },
      });

      if (!existingProduct) {
        throw new CustomError("Product not found", 404);
      }

      // Check if product has been used in any orders
      if (existingProduct.orderItems.length > 0) {
        throw new CustomError(
          "Cannot delete product that has been used in orders. Consider deactivating instead.",
          400
        );
      }

      await prisma.product.delete({
        where: { id },
      });

      logger.info(`Product deleted: ${existingProduct.name} (ID: ${id})`);
      return { message: "Product deleted successfully" };
    } catch (error) {
      logger.error("Product deletion failed:", error);
      throw error;
    }
  }

  static async updateStock(id: string, data: UpdateStockInput) {
    try {
      return await prisma.$transaction(async (tx) => {
        const product = await tx.product.findUnique({
          where: { id },
        });

        if (!product) {
          throw new CustomError("Product not found", 404);
        }

        if (!product.trackInventory) {
          throw new CustomError("This product does not track inventory", 400);
        }

        let newStockQuantity: number;

        switch (data.type) {
          case "ADD":
            newStockQuantity = product.stockQuantity + data.quantity;
            break;
          case "SUBTRACT":
            newStockQuantity = product.stockQuantity - data.quantity;
            if (newStockQuantity < 0) {
              throw new CustomError("Insufficient stock quantity", 400);
            }
            break;
          case "SET":
            newStockQuantity = data.quantity;
            if (newStockQuantity < 0) {
              throw new CustomError("Stock quantity cannot be negative", 400);
            }
            break;
          default:
            throw new CustomError("Invalid stock update type", 400);
        }

        const updatedProduct = await tx.product.update({
          where: { id },
          data: { stockQuantity: newStockQuantity },
          include: {
            category: {
              select: {
                id: true,
                name: true,
                isActive: true,
              },
            },
          },
        });

        logger.info(
          `Stock updated for product: ${product.name} (ID: ${id}). ` +
            `${data.type} ${data.quantity}. New stock: ${newStockQuantity}. ` +
            `Reason: ${data.reason || "Not specified"}`
        );

        return updatedProduct;
      });
    } catch (error) {
      logger.error("Stock update failed:", error);
      throw error;
    }
  }
}
